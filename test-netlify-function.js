// test-netlify-function.js
// Test the Netlify function locally to verify it works

const { handler } = require('./netlify/functions/process');

async function testNetlifyFunction() {
    console.log('🧪 Testing Netlify function locally...');

    // Mock event object with highly AI-generated text (maximum challenge)
    const event = {
        httpMethod: 'POST',
        path: '/.netlify/functions/process',
        body: JSON.stringify({
            text: "Furthermore, it is important to note that this comprehensive analysis demonstrates significant improvements in the implementation of advanced methodologies. The utilization of these innovative approaches facilitates enhanced performance optimization. Moreover, the evaluation of these systems reveals substantial benefits. Additionally, the framework provides effective solutions for complex problems. Therefore, we can conclude that this methodology is highly efficient and demonstrates excellent results. It should be mentioned that the substantial evidence supports these findings. Consequently, the optimization process yields remarkable outcomes.",
            styleStrength: 80
        })
    };

    // Mock context object
    const context = {
        functionName: 'process',
        functionVersion: '1.0',
        memoryLimitInMB: 1024,
        getRemainingTimeInMillis: () => 9000
    };

    try {
        console.log('📝 Input text:', JSON.parse(event.body).text);
        console.log('🚀 Calling function...');
        
        const result = await handler(event, context);
        
        console.log('✅ Function completed');
        console.log('📊 Status Code:', result.statusCode);
        
        if (result.statusCode === 200) {
            const response = JSON.parse(result.body);
            console.log('🎉 Success!');
            console.log('📝 Original length:', response.processingStats?.originalLength || 'unknown');
            console.log('📝 Final length:', response.processingStats?.finalLength || 'unknown');
            console.log('⏱️ Processing time:', response.processingStats?.totalTime || 'unknown', 'ms');
            console.log('🔄 Method attempts:', response.processingStats?.methodAttempts?.length || 0);
            console.log('📄 Modified text:', response.modifiedText);
            
            // Show transformation
            const original = JSON.parse(event.body).text;
            const modified = response.modifiedText;
            
            console.log('\n🔍 Transformation Analysis:');
            console.log('Original:', original);
            console.log('Modified:', modified);
            console.log('Changed:', original !== modified ? 'YES' : 'NO');
            
            if (original !== modified) {
                const changeRate = ((Math.abs(original.length - modified.length) / original.length) * 100).toFixed(1);
                console.log('Change rate:', changeRate + '%');
            }
            
        } else {
            console.log('❌ Function failed');
            console.log('Error response:', result.body);
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

// Run the test
testNetlifyFunction();
