// netlify/functions/text-transformations.js
// Simple text transformations for Netlify deployment
// Self-contained, no external dependencies

/**
 * Add controlled mistakes to make text more human-like
 */
function addControlledMistakes(text, errorRate = 0.05) {
    let result = text;
    
    // 1. Occasional double spaces
    result = result.replace(/\. /g, (match) => {
        return Math.random() < errorRate * 0.3 ? '.  ' : match;
    });
    
    // 2. Minor punctuation inconsistencies
    result = result.replace(/,(\w)/g, (match, letter) => {
        return Math.random() < errorRate * 0.2 ? `, ${letter}` : match;
    });
    
    // 3. Casual intensifiers
    result = result.replace(/\b(very|really|quite|pretty)\s+(\w+)/g, (match, adverb, adjective) => {
        if (Math.random() < errorRate * 0.15) {
            const casualIntensifiers = [
                `${adjective} as hell`,
                `super ${adjective}`,
                `crazy ${adjective}`,
                `insanely ${adjective}`
            ];
            return casualIntensifiers[Math.floor(Math.random() * casualIntensifiers.length)];
        }
        return match;
    });
    
    // 4. Add natural hesitation
    const fillerWords = ['um', 'uh', 'well', 'like', 'you know', 'I mean'];
    result = result.replace(/\b(so|and|but)\s+/gi, (match) => {
        if (Math.random() < errorRate * 0.08) {
            const filler = fillerWords[Math.floor(Math.random() * fillerWords.length)];
            return `${match.trim()}, ${filler}, `;
        }
        return match;
    });
    
    return result;
}

/**
 * Change writing style to be more casual (clean version)
 */
function changeStyle(text) {
    let result = text;

    // Replace formal sentence starters (conservative)
    const formalStarters = {
        'It is important to note that': 'Here\'s the thing -',
        'It should be mentioned that': 'Also,',
        'It is worth noting that': 'What\'s interesting is',
        'It must be emphasized that': 'This is key -',
        'It is essential to understand': 'You should know'
    };

    Object.entries(formalStarters).forEach(([formal, casual]) => {
        const regex = new RegExp(formal.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
        result = result.replace(regex, casual);
    });

    // Add conversational connectors (very conservative)
    result = result.replace(/\. ([A-Z])/g, (match, letter) => {
        if (Math.random() < 0.15) { // Reduced from 0.25 to 0.15
            const connectors = ['. And ', '. But ', '. So '];
            return connectors[Math.floor(Math.random() * connectors.length)] + letter.toLowerCase();
        }
        return match;
    });

    return result;
}

/**
 * Simple paraphrasing using basic transformations
 */
function simpleParaphrase(text) {
    let result = text;
    
    // Basic word replacements
    const replacements = {
        'big': 'large',
        'small': 'tiny',
        'good': 'great',
        'bad': 'terrible',
        'fast': 'quick',
        'slow': 'sluggish',
        'easy': 'simple',
        'hard': 'difficult',
        'new': 'fresh',
        'old': 'ancient'
    };
    
    Object.entries(replacements).forEach(([original, replacement]) => {
        const regex = new RegExp(`\\b${original}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            if (Math.random() < 0.3) {
                return match[0] === match[0].toUpperCase() ? 
                    replacement.charAt(0).toUpperCase() + replacement.slice(1) : 
                    replacement;
            }
            return match;
        });
    });
    
    return result;
}

/**
 * Quality check for processed text
 */
function qualityCheck(text) {
    const issues = [];
    
    // Check for excessive repetition
    const words = text.toLowerCase().match(/\b\w+\b/g) || [];
    const wordCount = {};
    words.forEach(word => {
        wordCount[word] = (wordCount[word] || 0) + 1;
    });
    
    const totalWords = words.length;
    const repeatedWords = Object.entries(wordCount)
        .filter(([word, count]) => count > totalWords * 0.05 && word.length > 3)
        .map(([word]) => word);
    
    if (repeatedWords.length > 0) {
        issues.push(`Excessive repetition: ${repeatedWords.join(', ')}`);
    }
    
    // Check for very short sentences
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const shortSentences = sentences.filter(s => s.trim().split(/\s+/).length < 3);
    
    if (shortSentences.length > sentences.length * 0.3) {
        issues.push('Too many short sentences');
    }
    
    // Check for missing punctuation
    if (!text.match(/[.!?]$/)) {
        issues.push('Missing ending punctuation');
    }
    
    return {
        hasIssues: issues.length > 0,
        issues: issues,
        score: Math.max(0, 100 - (issues.length * 20))
    };
}

/**
 * Balanced humanization combining multiple techniques (clean version)
 */
function balancedHumanization(text, styleProfile = null, styleStrength = 0, options = {}) {
    const { aggressiveness = 0.7 } = options;

    let result = text;

    // Apply minimal transformations
    if (aggressiveness > 0.5) {
        result = changeStyle(result);
    }

    // Very conservative mistake injection
    result = addControlledMistakes(result, aggressiveness * 0.05);

    // Apply style if provided (conservative)
    if (styleProfile && styleStrength > 0) {
        const intensity = styleStrength / 100;

        if (styleProfile.name === 'casual') {
            result = addControlledMistakes(result, intensity * 0.08);
        } else if (styleProfile.name === 'professional') {
            // Keep more formal but still humanized
            result = addControlledMistakes(result, intensity * 0.03);
        }
    }

    return result;
}

/**
 * Simple text transformations without external dependencies
 */
function simpleTextTransformations(text, options = {}) {
    const { aggressiveness = 0.7, addMistakes = true, changeStyleFlag = true } = options;
    
    let result = text;
    
    if (changeStyleFlag) {
        result = changeStyle(result);
    }
    
    if (addMistakes) {
        result = addControlledMistakes(result, aggressiveness * 0.1);
    }
    
    return result;
}

module.exports = {
    addControlledMistakes,
    changeStyle,
    simpleParaphrase,
    qualityCheck,
    balancedHumanization,
    simpleTextTransformations
};
